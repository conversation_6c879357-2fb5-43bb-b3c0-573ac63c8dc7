/* ============================================
   Main Stylesheet
   ============================================ */

/* 1. Import Partials */
@import 'partials/_variables.css';
@import 'partials/_base.css';
@import 'partials/_layout.css';
@import 'partials/_components.css';
@import 'partials/_utilities.css';

.user-box + .user-box .cards-header {
    padding: 20px 30px;
    justify-content: space-between;
}
.user-box .cards-header,
.user-box .cards-view {
    display: flex;
    align-items: center;
    justify-content: center;
}
.user-box .cards-header svg,
.user-box .cards-view svg {
    width: 24px;
}
.user-box .cards-header .title,
.user-box .cards-view .title {
    margin: 0 16px;
    font-size: 15px;
}
.user-box .cards-header-date svg,
.user-box .cards-view-date svg {
    color: var(--color-cards-header-date-color);
    border-radius: 50%;
    padding: 5px;
    width: 30px;
}
.user-box .cards-header .cards-button,
.user-box .cards-view .cards-button {
    background: var(--bg-cards-button);
    padding: 10px 16px;
    margin-right: 0;
}
.user-box .cards-header .cards-button svg,
.user-box .cards-view .cards-button svg {
    width: 16px;
    margin: 0 2px;
}
.user-box .cards-header .date-wrapper,
.user-box .cards-view .date-wrapper {
    display: flex;
    align-items: center;
    margin: auto;
}
.user-box .cards-header .date-wrapper .title,
.user-box .cards-view .date-wrapper .title {
    margin: 0 16px;
}
.cards-view > svg {
    margin-right: 12px;
}
.today {
    position: relative;
}
.today:before {
    content: "";
    position: absolute;
    width: 5px;
    height: 5px;
    background-color: var(--color-today);
    bottom: -8px;
    right: 50%;
    border-radius: 50%;
}
.card {
    background: var(--gradient-card);
    padding: 40px 30px;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    max-height: 430px;
    width: 100%;
}
.card .title {
    font-size: 16px;
    font-weight: 500;
}
.card .subtitle {
    font-size: 13px;
    line-height: 1.6em;
}
.card + .card {
    margin-left: 20px;
}
.activity {
    max-width: 480px;
}
.activity .title {
    margin-bottom: 20px;
}
.activity-links {
    display: flex;
    align-items: center;
    margin-top: auto;
    font-size: 15px;
}
.activity-link {
    padding-bottom: 10px;
    position: relative;
    cursor: pointer;
    transition: 0.3s;
}
.activity-link + .activity-link {
    margin-left: 25px;
}
.activity-link + .activity-link:before {
    content: "";
    position: absolute;
    width: 5px;
    height: 5px;
    background-color: var(--color-today);
    top: -2px;
    right: -8px;
    border-radius: 50%;
}
.activity-link + .activity-link:hover:after {
    content: "";
    position: absolute;
    width: 22px;
    height: 2px;
    background: var(--color-accent);
    left: 0;
    bottom: 0;
}
.activity-link + .activity-link:hover {
    color: var(--color-light);
    -webkit-text-stroke: .3px var(--color-accent);
}
.activity-link.active {
    color: var(--color-light);
    font-weight: 500;
}
.activity-link.active:before {
    content: "";
    position: absolute;
    width: 22px;
    height: 2px;
    background: var(--color-accent);
    left: 0;
    bottom: 0;
}
.destination {
    display: flex;
    align-items: center;
    margin-top: auto;
}
.destination-card {
    background: var(--gradient-destination-45);
    padding: 20px;
    width: 100%;
    border-radius: 6px;
}
.destination-card + .destination-card {
    margin-left: 20px;
    background: var(--gradient-destination-325);
}
.destination-profile {
    display: flex;
    align-items: center;
    font-size: 14px;
}
.destination-profile svg {
    width: 18px;
    flex-shrink: 0;
    margin-right: 8px;
}
.destination-length {
    margin-left: auto;
    display: flex;
    align-items: center;
    font-size: 13px;
}
.destination-points {
    margin-top: 30px;
}
.profile-img {
    width: 46px;
    height: 46px;
    object-fit: cover;
    border-radius: 50%;
    padding: 2px;
    border: 1px solid var(--color-profile-border);
    flex-shrink: 0;
}
.point {
    font-size: 14px;
    color: var(--color-profile-border);
    font-weight: 500;
}
.sub-point {
    font-size: 13px;
    margin-top: 4px;
}
.discount {
    max-width: 320px;
    width: 100%;
}
.discount .title {
    margin-bottom: 30px;
}
.discount .subtitle {
    margin-bottom: 8px;
}
.discount .subtitle-count {
    font-size: 17px;
    color: var(--color-success);
    font-weight: 500;
}
.discount .subtitle-count + .subtitle {
    margin-top: 20px;
}
.discount .subtitle-count.dist {
    color: var(--color-danger);
}
.discount-wrapper {
    display: flex;
}
.discount-chart {
    margin-left: auto;
}
.discount-profile {
    display: flex;
    align-items: center;
    margin-top: 40px;
}
.discount-img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
    margin: 0 16px 0 12px;
}
.discount-name {
    font-weight: 500;
    font-size: 15px;
}
.discount-type {
    font-size: 13px;
    margin-top: 4px;
}
.circle {
    width: 100px;
    height: 100px;
    border: 3px solid var(--color-circle-border);
    border-radius: 50%;
    position: relative;
}
.pie {
    position: relative;
    width: 120px;
    height: 120px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%) rotate(-90deg);
}
.pie svg circle {
    fill: transparent;
    stroke: var(--color-pie);
    stroke-width: 14;
    stroke-dasharray: 275;
    stroke-dashoffset: 235;
    animation: pieChart 3.8s linear forwards;
}
.counter {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    color: var(--color-counter);
    font-weight: 500;
}
.offer-button {
    background: var(--color-offer-button-bg);
    padding: 14px;
    text-align: center;
    margin-top: auto;
    color: #fff;
    font-size: 13px;
    cursor: pointer;
}
.cards-header {
    background: var(--color-cards-header-bg);
    border-radius: 6px 6px 0 0;
    padding: 20px 45px;
    font-size: 14px;
    font-weight: 500;
}
.cards-header-date {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.cards-header-date svg {
    color: var(--color-cards-header-date-color);
    border-radius: 50%;
    padding: 5px;
    width: 30px;
}
.cards-wrapper {
    width: 100%;
}
.cards-hour {
    font-size: 26px;
}
.cards-hour .am-pm {
    font-size: 15px;
    font-weight: 500;
}
.cards.card {
    border-radius: 0 0 6px 6px;
    padding: 30px 20px;
}
.cards.card svg {
    width: 24px;
    margin-left: auto;
    color: var(--color-degree);
}
.cards-head {
    display: flex;
    align-items: center;
}
.degree {
    margin-top: 10px;
    font-size: 13px;
    display: flex;
    align-items: center;
    color: var(--color-degree);
    font-weight: 500;
}
.degree svg {
    width: 24px;
    margin-right: 12px;
}
.items {
    display: flex;
    flex-wrap: wrap;
    margin-top: 16px;
    font-size: 13px;
    font-weight: 500;
}
.items.numbers {
    margin-top: 0;
}
.item {
    flex: 0 1 calc(100% / 7);
    padding: 10px 5px;
    text-align: center;
}
.item.is-active {
    background: var(--color-accent);
    border-radius: 50%;
    color: #fff;
}
.item.disable {
    color: var(--text-muted);
}
.account {
    width: 100%;
    height: 180px;
    margin-top: auto;
    flex-grow: 0;
    position: relative;
    transition: 0.3s;
    cursor: pointer;
}
.account:hover {
    transform: scale(1.02);
}
.account:before {
    content: "";
    position: absolute;
    width: 24px;
    height: 24px;
    box-shadow: -15px 0 0 0 var(--color-warning);
    background: var(--color-today);
    top: 20px;
    left: 42px;
    border-radius: 50%;
}
.account-wrapper {
    max-width: 310px;
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-left: 20px;
    align-items: center;
}
.account-profile {
    margin: auto;
    position: relative;
    text-align: center;
}
.account-profile img {
    width: 84px;
    height: 84px;
    border-radius: 50%;
    object-fit: cover;
    object-position: left;
    border: 3px solid var(--color-accent);
    padding: 5px;
}
.account-profile .blob {
    position: absolute;
    border-radius: 50%;
    animation: fly 5.8s linear infinite alternate;
}
.account-profile .blob:nth-child(1) {
    width: 14px;
    height: 14px;
    top: 25px;
    left: -20px;
    background: var(--color-accent-dark);
    animation-delay: 0.9s;
}
.account-profile .blob:nth-child(2) {
    width: 18px;
    height: 18px;
    background: var(--color-danger-dark);
    right: -20px;
    top: -20px;
    animation-delay: 0.2s;
}
.account-profile .blob:nth-child(3) {
    width: 12px;
    height: 12px;
    background: var(--color-success-dark);
    right: -35px;
    top: 50%;
    animation-delay: 1.8s;
}
.account-name {
    margin: 20px 0 10px;
}
.account-title {
    font-size: 14px;
}
.account-cash {
    font-size: 22px;
    font-weight: 500;
    margin-bottom: 6px;
    padding-top: 16px;
    position: relative;
}
.account-cash:before {
    content: "";
    position: absolute;
    width: 5px;
    height: 5px;
    background: var(--color-primary);
    right: 10px;
    border-radius: 50%;
    box-shadow: -10px 0 0 0 var(--color-primary), 10px 0 0 0 var(--color-primary);
    top: 24px;
}
.account-income {
    font-size: 14px;
}
.account-iban {
    margin-top: auto;
    font-weight: 500;
}
.time {
    height: 24px;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: var(--bg-badge-warning);
    font-size: 13px;
    display: none; /* Default pro mobil */
    align-items: center;
    justify-content: center;
    color: #fff;
}
@media screen and (min-width: 1201px) {
    .time {
        display: inline-flex;
    }
}

/* Table Styles */
.table .status {
    color: var(--color-success);
    display: inline-flex;
    align-items: center;
}
.table .status svg {
    margin-right: 6px;
    width: 22px;
    height: 22px;
    padding: 3px;
    border-radius: 4px;
    background-color: var(--bg-badge-info);
    color: currentColor;
}
.table .status.is-red {
    color: var(--color-danger);
}
.table .status.is-red svg {
    background: #2e2142;
}
.table .status.is-wait {
    color: var(--color-wait);
    position: relative;
}
.table .status.is-wait:before {
    width: 22px;
    height: 22px;
    position: absolute;
    left: 0;
    top: 0;
    content: "";
    background: #1a214d;
    border-radius: 4px;
}
.table .status.is-wait svg {
    background-color: transparent;
    color: currentColor;
    animation: turn 2s linear infinite both;
}
.table {
    text-align: left;
    padding: 0;
}
.table th {
    font-size: 14px;
    font-weight: normal;
    padding-bottom: 16px;
}
.table th:nth-child(n+5) {
    padding: 0 10px 16px;
}
.table th:first-child {
    padding-left: 36px;
}
.table td {
    font-size: 15px;
    vertical-align: middle;
    padding: 8px 0;
}
.table td:last-of-type {
    width: 100px;
}
.table td:nth-child(n+5) {
    padding: 0 10px;
}
.table td:nth-last-of-type(2) svg {
    width: 16px;
}
.table input {
    appearance: none;
    width: 16px;
    height: 16px;
    border: 1px solid var(--color-table-input-border);
    vertical-align: middle;
    background-color: transparent;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 16px;
    transition: 0.25s;
    background-size: 0;
    background-position: center;
}
.table input:checked {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='24' height='24' stroke='%23fff' stroke-width='2' fill='none' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='20 6 9 17 4 12'%3E%3C/polyline%3E%3C/svg%3E");
    background-color: var(--color-table-input-bg);
    background-size: 12px;
    background-repeat: no-repeat;
}

/* ============================================
   8. Animations & Keyframes
   ============================================ */
@keyframes swing {
    0%, 30%, 50%, 70%, 100% {
        transform: rotate(0deg);
    }
    10% {
        transform: rotate(10deg);
    }
    40% {
        transform: rotate(-10deg);
    }
    60% {
        transform: rotate(5deg);
    }
    80% {
        transform: rotate(-5deg);
    }
}

@keyframes left {
    0% {
        transform: translateX(-30px);
        opacity: 0;
    }
    100% {
        opacity: 1;
        transform: none;
    }
}

@keyframes top {
    0% {
        transform: translateY(-30px);
        opacity: 0;
    }
    100% {
        opacity: 1;
        transform: none;
    }
}

@keyframes pieChart {
    100% {
        stroke-dashoffset: 0;
    }
}

@keyframes fly {
    40% {
        transform: translate(-6px, -6px);
    }
    60% {
        transform: translate(-12px, -2px);
    }
    100% {
        transform: translate(0, 0);
    }
}

@keyframes turn {
    100% {
        transform: rotate(360deg);
    }
}

/* ============================================
   9. Media Queries
   ============================================ */
@media (max-width: 480px) {
    .layout .sidebar.break-point-xs {
        position: fixed;
        left: -280px;
        height: 100%;
        top: 0;
        z-index: 100;
    }
    .layout .sidebar.break-point-xs.collapsed {
        left: -80px;
    }
    .layout .sidebar.break-point-xs.toggled {
        left: 0;
    }
    .layout .sidebar.break-point-xs.toggled ~ .overlay {
        display: block;
    }
    .layout .sidebar.break-point-xs ~ .layout .header {
        width: 100% !important;
        transition: none;
    }
}

@media (max-width: 576px) {
    .layout .sidebar.break-point-sm {
        position: fixed;
        left: -280px;
        height: 100%;
        top: 0;
        z-index: 100;
    }
    .layout .sidebar.break-point-sm.collapsed {
        left: -80px;
    }
    .layout .sidebar.break-point-sm.toggled {
        left: 0;
    }
    .layout .sidebar.break-point-sm.toggled ~ .overlay {
        display: block;
    }
    .layout .sidebar.break-point-sm ~ .layout .header {
        width: 100% !important;
        transition: none;
    }
}

@media (max-width: 768px) {
    .layout .sidebar.break-point-md {
        position: fixed;
        left: -280px;
        height: 100%;
        top: 0;
        z-index: 100;
    }
    .layout .sidebar.break-point-md.collapsed {
        left: -80px;
    }
    .layout .sidebar.break-point-md.toggled {
        left: 0;
    }
    .layout .sidebar.break-point-md.toggled ~ .overlay {
        display: block;
    }
    .layout .sidebar.break-point-md ~ .layout .header {
        width: 100% !important;
        transition: none;
    }
}

@media (max-width: 992px) {
    .layout .sidebar.break-point-lg {
        position: fixed;
        left: -280px;
        height: 100%;
        top: 0;
        z-index: 100;
    }
    .layout .sidebar.break-point-lg.collapsed {
        left: -80px;
    }
    .layout .sidebar.break-point-lg.toggled {
        left: 0;
    }
    .layout .sidebar.break-point-lg.toggled ~ .overlay {
        display: block;
    }
    .layout .sidebar.break-point-lg ~ .layout .header {
        width: 100% !important;
        transition: none;
    }
}

@media (max-width: 1200px) {
    .layout .sidebar.break-point-xl {
        position: fixed;
        left: -280px;
        height: 100%;
        top: 0;
        z-index: 100;
    }
    .layout .sidebar.break-point-xl.collapsed {
        left: -80px;
    }
    .layout .sidebar.break-point-xl.toggled {
        left: 0;
    }
    .layout .sidebar.break-point-xl.toggled ~ .overlay {
        display: block;
    }
    .layout .sidebar.break-point-xl ~ .layout .header {
        width: 100% !important;
        transition: none;
    }
}

@media (max-width: 1600px) {
    .layout .sidebar.break-point-xxl {
        position: fixed;
        left: -280px;
        height: 100%;
        top: 0;
        z-index: 100;
    }
    .layout .sidebar.break-point-xxl.collapsed {
        left: -80px;
    }
    .layout .sidebar.break-point-xxl.toggled {
        left: 0;
    }
    .layout .sidebar.break-point-xxl.toggled ~ .overlay {
        display: block;
    }
    .layout .sidebar.break-point-xxl ~ .layout .header {
        width: 100% !important;
        transition: none;
    }
}

@media screen and (max-width: 1500px) {
    .wrapper {
        max-width: 1200px;
    }
    .activity {
        width: 49%;
    }
    .discount {
        width: 48%;
        height: 100%;
    }
    .first-box {
        flex-wrap: wrap;
    }
    .first-box .cards-wrapper {
        width: 67%;
        margin: 20px 0;
    }
    .first-box .cards-wrapper .item.is-active {
        background: none;
        color: inherit;
    }
    .first-box .account-wrapper {
        width: calc(33% - 20px);
        margin: 20px 0 20px 20px;
    }
    .second-box {
        flex-wrap: wrap;
    }
    .second-box .cards-wrapper {
        margin-top: 40px;
        width: 100%;
    }
}

@media screen and (max-width: 1200px) {
    .time {
        display: none;
    }
}

@media screen and (max-width: 1060px) {
    .user-info .button,
    .user-info .hour {
        display: none;
    }
}

@media screen and (max-width: 1020px) {
    .user-box .cards-view {
        display: none;
    }
    .user-box .cards-header .cards-button {
        display: none;
    }
    .cards-header-date {
        margin: auto;
    }
}

@media screen and (max-width: 930px) {
    .second-box .cards-wrapper {
        width: 100%;
    }
    .header-link {
        display: none;
    }
    .user-info .profile {
        margin-right: 0;
    }
}

@media screen and (max-width: 850px) {
    .activity-card,
    .discount {
        width: 100%;
    }
    .user-box .discount {
        margin-left: 0;
        margin-top: 20px;
    }
    .left-side {
        display: none;
    }
    .main-container {
        padding-left: 25px;
    }
    .activity-links,
    .destination {
        margin-top: 20px;
    }
}

@media screen and (max-width: 720px) {
    .first-box .account-wrapper {
        display: none;
    }
    .first-box .cards-wrapper {
        width: 100%;
    }
    .second-box .cards.card {
        overflow-y: auto;
    }
}

@media screen and (max-width: 420px) {
    .destination-card + .destination-card {
        display: none;
    }
}

